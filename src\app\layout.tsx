import type { Metadata } from "next";
import { Pixelify_Sans } from "next/font/google";
import "@/styles/globals.css";
import ThemeProvider from "@/components/providers/theme-provider";

const pixelify = Pixelify_Sans({
  variable: "--font-pixelify-sans",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Mystique",
  description: "Dungeon and Dragons first edition and pre release online lobby",
};

export default ({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) => {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${pixelify.variable} antialiased`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}
