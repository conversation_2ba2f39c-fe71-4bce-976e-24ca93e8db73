"use client";

import { <PERSON><PERSON> } from "../ui/button";
import { authClient } from "@/lib/auth-client";
import { Github } from "lucide-react";

export default () => {
  return (
    <Button
      onClick={async () =>
        await authClient.signIn.social({
          provider: "github",
          callbackURL: "/dashboard",
        })
      }
    >
      <Github />
      Github
    </Button>
  );
};  
